<template>
  <view class="message-item" :class="message.type + '-message'">
    <!-- AI消息 -->
    <view v-if="message.type === 'ai'" class="ai-message-wrapper">
      <view class="avatar-wrapper">
        <view class="ai-avatar">
          <view class="robot-icon">
              <image class="robot-image" src="https://portal.gxetc.com.cn/public-static/img/miniprogram/static/evaluation/gxt_logo.png" mode="aspectFill" />
          </view>
        </view>
      </view>
      <view class="message-content">
        <!-- 文本消息 -->
        <view v-if="!message.isHtml" class="text-bubble ai-bubble">
          <text class="message-text">{{ displayContent }}</text>
          <view v-if="isTyping" class="typing-cursor"></view>

          <!-- AI消息语音播放按钮 -->
          <view v-if="!isTyping" class="voice-play-btn" @click="playVoice(message.content)">
            <image src="/static/agent/voice-play.png" mode="aspectFill" />
          </view>
        </view>

        <!-- 富文本消息 -->
        <view v-else class="text-bubble ai-bubble">
          <rich-text :nodes="isTyping ? (localTypingContent || typingContent) : message.content" class="rich-content"></rich-text>
          <view v-if="isTyping" class="typing-cursor"></view>

          <!-- AI富文本消息语音播放按钮 -->
          <view v-if="!isTyping" class="voice-play-btn" @click="playVoice(message.content.replace(/<[^>]*>/g, ''))">
            <image src="/static/agent/voice-play.png" mode="aspectFill" />
          </view>
        </view>
      </view>
    </view>

    <!-- 用户消息 -->
    <view v-else class="user-message-wrapper">
      <view class="message-content">
        <view class="text-bubble user-bubble">
          <text class="message-text">{{ message.content }}</text>
        </view>
      </view>
      <view class="avatar-wrapper">
        <view class="user-avatar">
          <view class="user-icon">
            <view class="user-head"></view>
            <view class="user-body"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 景点卡片独立布局 -->
    <view v-if="cards && cards.length > 0 && !isTyping" class="cards-container" :class="{'cards-folded': isFolded}">
      <view class="cards-header" @click="toggleFold">
        <uni-icons type="location-filled" size="18" color="#5591FF" />
        <text class="cards-title">推荐景区</text>
        <view class="fold-icon">
          <uni-icons :type="isFolded ? 'bottom' : 'top'" size="14" color="#999" />
        </view>
      </view>

      <view class="cards-list" v-if="!isFolded">
        <view
          v-for="card in cards"
          :key="card.id"
          class="card-item"
          @click="handleCardClick(card)"
        >
          <view class="card-image">
            <image v-if="card.imageUrl" :src="card.imageUrl" mode="aspectFill" class="card-img" />
            <view v-else class="card-img-placeholder"></view>
            <view v-if="card.rating" class="card-rating">{{ card.rating }}</view>
          </view>
          <view class="card-content">
            <view class="card-title">{{ card.title }}</view>
            <view v-if="card.description" class="card-desc">{{ card.description }}</view>
          </view>
          <view class="card-arrow">
            <uni-icons type="right" size="16" color="#ccc" />
          </view>
        </view>
      </view>
    </view>

    <!-- 时间显示 -->
    <view v-if="shouldShowTime" class="time-display">
      <text class="time-text">{{ formatTime(message.timestamp) }}</text>
    </view>
  </view>
</template>

<script>
// 引入uni-icons组件
import uniIcons from '@/components/uni-icons/uni-icons.vue'

export default {
  name: 'AgentMessageItem',
  components: {
    uniIcons
  },
  props: {
    message: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      default: 0
    },
    isTyping: {
      type: Boolean,
      default: false
    },
    typingContent: {
      type: String,
      default: ''
    },
    cards: {
      type: Array,
      default: () => []
    },
    shouldShowTime: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isEntering: false,
      isFolded: false,
      // 本地打字显示状态
      localTypingContent: '',
      typingTimer: null,
      lastTypingUpdate: 0
    }
  },
  mounted() {
    this.triggerEnterAnimation()
  },
  watch: {
    // 监听打字内容变化，优化更新频率
    typingContent() {
      if (this.isTyping) {
        // 使用更精细的节流，每100ms最多触发一次，提升流畅度
        if (!this._contentUpdateTimer) {
          this._contentUpdateTimer = setTimeout(() => {
            this.$emit('contentUpdated')
            this._contentUpdateTimer = null
          }, 100)
        }
      }
    },

    // 监听打字状态变化
    isTyping(newVal, oldVal) {
      if (newVal && !oldVal) {
        // 开始打字时重置显示状态
        this.resetTypingDisplay()
      } else if (!newVal && oldVal) {
        // 打字结束时清理定时器
        this.cleanupTypingTimers()
      }
    }
  },
  computed: {
    // 计算显示的内容，优化打字效果
    displayContent() {
      if (!this.isTyping) {
        return this.message.content
      }
      // 使用本地状态控制显示内容，减少父组件更新频率
      return this.localTypingContent || this.typingContent
    }
  },

  methods: {
    // 触发渐入动画
    triggerEnterAnimation() {
      this.$nextTick(() => {
        setTimeout(() => {
          this.isEntering = true
          // 通知父组件滚动到底部
          this.$emit('contentRendered')
        }, this.index * 100) // 每个消息延迟100ms
      })
    },

    // 重置打字显示状态
    resetTypingDisplay() {
      this.localTypingContent = ''
      this.lastTypingUpdate = Date.now()
    },

    // 清理打字相关定时器
    cleanupTypingTimers() {
      if (this._contentUpdateTimer) {
        clearTimeout(this._contentUpdateTimer)
        this._contentUpdateTimer = null
      }
      if (this.typingTimer) {
        clearTimeout(this.typingTimer)
        this.typingTimer = null
      }
    },
    
    // 处理卡片点击
    handleCardClick(card) {
      this.$emit('cardClick', card)
    },
    
    // 展示更多卡片
    showMoreCards() {
      this.$emit('showMoreCards')
    },

    // 切换折叠状态
    toggleFold() {
      this.isFolded = !this.isFolded
      this.$emit('toggleFold', this.isFolded)
    },

    // 播放语音
    playVoice(content) {
      this.$emit('playVoice', content)
    },

    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      return `${hours}:${minutes}`
    }
  },

  // 组件销毁时清理资源
  beforeDestroy() {
    this.cleanupTypingTimers()
  }
}
</script>

<style lang="scss" scoped>
.message-item {
  margin-bottom: 32rpx;

  &.ai-message {
    .ai-message-wrapper {
      display: flex;
      align-items: flex-start;

      .message-content {
        flex: 1;
        max-width: calc(100% - 120rpx);
      }
    }
  }

  &.user-message {
    .user-message-wrapper {
      display: flex;
      align-items: flex-start;
      justify-content: flex-end;

      .message-content {
        flex: 1;
        max-width: calc(100% - 120rpx);
        display: flex;
        align-items: flex-end;
      }
    }
  }
}

.message-content {
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease-out;
}

.avatar-wrapper {
  flex-shrink: 0;
}

.ai-avatar, .user-avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-avatar {
  margin-right: 20rpx;
}

.user-avatar {
  margin-left: 20rpx;
  background: linear-gradient(135deg, #FF8C42 0%, #E67E22 100%);
}

// AI机器人图标样式
.robot-icon {
  position: relative;
  width: 70rpx;
  height: 70rpx;
}

.robot-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}


// 用户头像图标样式
.user-icon {
  position: relative;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.user-head {
  width: 12rpx;
  height: 12rpx;
  background-color: #fff;
  border-radius: 50%;
  margin-bottom: 2rpx;
}

.user-body {
  width: 20rpx;
  height: 14rpx;
  background-color: #fff;
  border-radius: 10rpx 10rpx 0 0;
}

.text-bubble {
  padding: 14rpx 20rpx;
  border-radius: 8rpx;
  word-wrap: break-word;
  position: relative;
  font-size: 28rpx;
  color: #333333;

  &.ai-bubble {
    background-color: #fff;
    padding-right: 64rpx;  // 为语音按钮留出空间
    border-bottom-left-radius: 8rpx;
  }

  &.user-bubble {
    background: #BCDEFD;
  }
}

.message-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.4;
}

.typing-cursor {
  display: inline-block;
  width: 4rpx;
  height: 32rpx;
  background-color: #8e65de;
  vertical-align: middle;
  margin-left: 4rpx;
  animation: blink 1.2s infinite; // 稍微慢一点的闪烁
  border-radius: 2rpx; // 添加圆角让光标更柔和
}

/* 优化的打字动画样式 - 仿照参考文件 */
.text-typing-animation {
  overflow: hidden;
  white-space: pre-wrap; // 改为pre-wrap支持换行
  word-wrap: break-word;
}

.text-typing-complete {
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 减少重绘的优化样式 */
.message-text {
  backface-visibility: hidden;
  transform: translateZ(0);
  will-change: auto;
  white-space: pre-wrap; // 支持换行显示
  word-wrap: break-word;
}

.typing-content {
  backface-visibility: hidden;
  transform: translateZ(0);
  white-space: pre-wrap;
  word-wrap: break-word;
}

.rich-content {
  font-size: 32rpx;
  line-height: 1.4;
  color: #333;
}

.voice-play-btn {
  position: absolute;
  right: 16rpx;
  top: 40rpx;
  transform: translateY(-50%);
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f0f0f0;
  z-index: 2;
  image {
    width: 100%;
    height: 100%;
  }
}

.time-display {
  text-align: center;
  margin: 24rpx 0;

  .time-text {
    font-size: 24rpx;
    color: #999;
  }
}

// 景点卡片样式
.cards-container {
  margin-top: 24rpx;
  width: 100%;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;

  &.cards-folded {
    border-radius: 16rpx;
  }
}

.cards-header {
  padding: 24rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;

  .cards-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
    margin-left: 12rpx;
  }

  .fold-icon {
    position: absolute;
    right: 24rpx;
    top: 50%;
    transform: translateY(-50%);
  }
}

.cards-list {
  padding: 8rpx 0;
}

.card-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  transition: background-color 0.2s ease;
  position: relative;

  &:active {
    background-color: #f8f9fa;
  }

  &:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 24rpx;
    right: 24rpx;
    bottom: 0;
    height: 1rpx;
    background-color: #f5f5f5;
  }
}

.card-image {
  width: 140rpx;
  height: 110rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 24rpx;
  position: relative;
  flex-shrink: 0;

  .card-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .card-img-placeholder {
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;
  }

  .card-rating {
    position: absolute;
    top: 12rpx;
    right: 12rpx;
    background-color: rgba(255, 255, 255, 0.9);
    color: #5591FF;
    font-size: 22rpx;
    padding: 4rpx 8rpx;
    border-radius: 6rpx;
    font-weight: 600;
  }
}

.card-content {
  flex: 1;
  overflow: hidden;

  .card-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .card-desc {
    font-size: 24rpx;
    color: #999;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.card-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 16rpx;

  .arrow-icon {
    font-size: 36rpx;
    color: #ccc;
  }
}

@keyframes blink {
  0%, 45% {
    opacity: 1;
  }
  50%, 95% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>
