<template>
  <view class="agent-input-panel">
    <!-- 快捷功能按钮和一键清空 -->
    <view class="actions-section">
      <!-- 一键清空 -->
      <view class="clear-btn-small" @click="handleClearChat">
        <text class="clear-text-small">一键清空</text>
        <image src="/static/agent/clear.png" mode="aspectFill" />
      </view>

      <!-- 快捷功能按钮 -->
      <view class="quick-actions">
        <view class="action-btn" @click="quickAction('hotel')">
          <uni-icons type="home" size="16" color="#007AFF" />
          <text class="action-text">酒店推荐</text>
        </view>
        <view class="action-btn" @click="quickAction('food')">
          <uni-icons type="circle" size="16" color="#007AFF" />
          <text class="action-text">美食推荐</text>
        </view>
        <view class="action-btn" @click="quickAction('scenic')">
          <uni-icons type="location" size="16" color="#007AFF" />
          <text class="action-text">景点推荐</text>
        </view>
        <view class="action-btn" @click="quickAction('plan')">
          <uni-icons type="home" size="16" color="#007AFF" />
          <text class="action-text">游玩规划</text>
        </view>
      </view>
    </view>

    <!-- 输入和录音切换区域 -->
    <view class="input-action-area">
      <!-- 文本输入模式 @input="handleInputDebounced" -->
      <view v-if="isTextMode" class="text-input-container">
        <input
          v-model="localInputText"
          class="text-input"
          placeholder="请输入您的问题"
          :maxlength="1000"
          :max-height="120"
          :cursor-spacing="20"
          :show-confirm-bar="false"
          :disable-default-padding="true"
          :auto-height="true"
          ref="textInput"
        />
      </view>

      <!-- 语音输入模式 -->
      <view v-else class="voice-input-container">
        <view class="voice-btn" :class="{ 'recording': isRecording }"
              @touchstart="handleVoiceStart"
              @touchend="handleVoiceEnd">
          <uni-icons type="mic" size="24" color="#fff" />
          <text class="voice-btn-text">{{ isRecording ? '松开结束' : '按住说话' }}</text>
        </view>
      </view>

      <!-- 右侧操作按钮 -->
      <view class="input-actions">
        <view v-if="isTextMode && hasContent" class="send-btn" @click="handleSend">
          <image src="/static/agent/send.png" mode="aspectFill" />
        </view>
        <view v-else class="switch-btn" @click="toggleInputMode">
          <image src="/static/agent/voice-text.png" mode="aspectFill" />
        </view>
      </view>
    </view>
  </view>
</template>

<script>
// 引入uni-icons组件
import uniIcons from '@/components/uni-icons/uni-icons.vue'

export default {
  name: 'AgentInputPanel',
  components: {
    uniIcons
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    isRecording: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isTextMode: true, // 是否为文本输入模式
      keyboardHeight: 0, // 键盘高度
      localInputText: '', // 本地输入文本
      inputDebounceTimer: null // 防抖定时器
    }
  },
  computed: {
    // 判断是否有输入内容
    hasContent() {
      return this.localInputText && this.localInputText.length > 0
    }
  },
  watch: {
    // 监听外部value变化，同步到本地
    value: {
      handler(newVal) {
        if (newVal !== this.localInputText) {
          this.localInputText = newVal
        }
      },
      immediate: true
    }
  },
  created() {
    // 监听键盘高度变化
    uni.onKeyboardHeightChange(res => {
      this.keyboardHeight = res.height
      this.$emit('keyboardHeightChange', res.height)
    })
  },
  methods: {
    // 防抖处理输入事件
    handleInputDebounced() {
      // 清除之前的定时器
      if (this.inputDebounceTimer) {
        clearTimeout(this.inputDebounceTimer)
      }
      
      // 设置新的定时器
      this.inputDebounceTimer = setTimeout(() => {
        this.$emit('input', this.localInputText)
      }, 100) // 100ms防抖延迟
    },
    
    // 处理发送消息
    handleSend() {
      if (!this.localInputText.trim()) return
      
      // 发送消息
      this.$emit('sendMessage', this.localInputText.trim())
      
      // 发送后清空本地输入
      this.localInputText = ''
      // 立即同步给父组件
      this.$emit('input', '')
      
      // 收起键盘
      this.hideKeyboard()
    },
    
    // 收起键盘
    hideKeyboard() {
      try {
        // 方法1：让输入框失去焦点
        if (this.$refs.textInput) {
          this.$refs.textInput.blur()
        }
        
        // 方法2：使用uni-app API收起键盘（兼容性处理）
        if (uni.hideKeyboard) {
          uni.hideKeyboard()
        }
      } catch (error) {
        console.log('收起键盘失败:', error)
      }
    },

    // 处理语音录制开始
    handleVoiceStart() {
      this.$emit('startRecord')
    },

    // 处理语音录制结束
    handleVoiceEnd() {
      this.$emit('stopRecord')
    },

    // 切换输入模式
    toggleInputMode() {
      this.isTextMode = !this.isTextMode
      
      // 如果切换到文本模式，自动聚焦输入框
      if (this.isTextMode) {
        this.$nextTick(() => {
          if (this.$refs.textInput) {
            this.$refs.textInput.focus()
          }
        })
      }
    },

    // 清空聊天记录
    handleClearChat() {
      this.$emit('clearChat')
    },

    // 快捷操作
    quickAction(type) {
      const actions = {
        hotel: '请推荐一些优质的酒店',
        food: '请推荐当地特色美食',
        scenic: '请推荐热门景点',
        plan: '请帮我制定旅游规划'
      }

      if (actions[type]) {
        this.$emit('sendMessage', actions[type])
      }
    }
  },
  
  // 组件销毁时清理定时器
  beforeDestroy() {
    if (this.inputDebounceTimer) {
      clearTimeout(this.inputDebounceTimer)
    }
  }
}
</script>

<style lang="scss" scoped>
.agent-input-panel {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 16rpx 24rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}



.actions-section {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.clear-btn-small {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background-color: transparent;
  border-radius: 16rpx;
  align-self: center;

  .clear-text-small {
    font-size: 20rpx;
    color: #666666;
    margin-right: 10rpx;
  }

  image {
    width: 32rpx;
    height: 32rpx;
  }
}

.quick-actions {
  display: flex;
  justify-content: space-between;
  gap: 12rpx;
}

.action-btn {
  height: 62rpx;
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 14rpx 16rpx;
  background: #FFFFFF;
  border-radius: 8rpx;
  gap: 6rpx;

  .action-text {
    font-size: 24rpx;
    color: #666666;
    font-weight: 400;
  }

  &:active {
    background-color: #f5f5f5;
  }
}

// 输入和录音切换区域
.input-action-area {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.text-input-container {
  flex: 1;
  background-color: #FFFFFF;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;

  .text-input {
    width: 100%;
    font-size: 32rpx;
    line-height: 1.4;
    background-color: transparent;
    min-height: 48rpx;
    max-height: 120rpx;
  }
}

.voice-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  .voice-btn {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;

    &.recording {
      background: linear-gradient(135deg, #FF6B6B 0%, #EE5A52 100%);
      animation: pulse 1s infinite;
    }

    &:active {
      transform: scale(0.98);
    }

    .voice-btn-text {
      font-size: 28rpx;
      color: #fff;
      font-weight: 500;
    }
  }
}

.input-actions {
  display: flex;
  align-items: center;
  gap: 8rpx;

  .switch-btn {
    width: 68rpx;
    height: 68rpx;
    border-radius: 24rpx;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    image {
      width: 100%;
      height: 100%;
    }
    &:active {
      background-color: #e0e0e0;
    }
  }

  .send-btn {
    width: 48rpx;
    height: 48rpx;
    border-radius: 24rpx;
    background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    image {
      width: 100%;
      height: 100%;
    }
    &:active {
      transform: scale(0.95);
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
</style>
