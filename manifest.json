{
    "name" : "etc-app",
    "appid" : "__UNI__181CAF4",
    "description" : "",
    "versionName" : "1.0.5",
    "versionCode" : "100",
    "transformPx" : false,
    "networkTimeout" : {
        "request" : 60000
    },
    "app-plus" : {
        /* 5+App特有相关 */
        "usingComponents" : true,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "modules" : {
            "OAuth" : {},
            "Bluetooth" : {}
        },
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                /* android打包配置 */
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH_ADMIN\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH_PRIVILEGED\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            "ios" : {
                "idfa" : false
            },
            /* ios打包配置 */
            "sdkConfigs" : {
                "oauth" : {},
                "ad" : {}
            }
        }
    },
    /* SDK配置 */
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        "appid" : "wx88bac29bcac3945e",
        "setting" : {
            "urlCheck" : false,
            "minified" : true,
            "es6" : false
        },
        "permission" : {
            "scope.userLocation" : {
                "desc" : "你的位置信息将用于小程序位置接口的效果展示"
            },
            "scope.record" : {
                "desc" : "需要使用你的麦克风权限，用于录音和语音识别功能"
            }
        },
        "requiredPrivateInfos" : [ "chooseAddress", "getLocation", "chooseLocation" ],
        "usingComponents" : true,
        "optimization" : {
            "subPackages" : true
        },
        "plugins" : {
            "captcha" : {
                "version" : "2.1.0", //请选择小程序插件最新版本
                "provider" : "wx1fe8d9a3cb067a75"
            },
            "WechatSI" : {
                "version" : "0.3.6",
                "provider" : "wx069ba97219f66d99"
            }
        },
        // "monthly-bill-plugin":{
        // 	"version": "dev-21d3a53ba41220cda0cb87ca6fe38b30",
        // 	"provider": "wx22e84d3d44639821",
        // 	"export": "index.js"
        // }
        "navigateToMiniProgramAppIdList" : [],
        "__usePrivacyCheck__" : true
    },
    "sassImplementationName" : "node-sass"
}
