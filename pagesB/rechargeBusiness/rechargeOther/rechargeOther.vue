<template>
	<view class="sellPay-container">
		<view class="weui-form">
			<view class="weui-cells__title">
				车主信息
			</view>
			<view class="weui-cells" style="overflow: revert;">
				<licensecolor desc='非车身颜色' :palteColor='vehicleData.vehicle_color' @on-change='onLicensecolorChange'>
				</licensecolor>
				<view class="vux-x-input weui-cell">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require" style="width: 200rpx;">车牌号码</view>
					</view>
					<view class="weui-cell__primary g-flex g-flex-end">
						<!-- <input v-model="vehicleData.vehicle_code" class="weui-input" placeholder="请输入代充车牌号码" /> -->
						<view class="input-wrapper">
							<input placeholder="请点击输入车牌号码" @tap="selectVehicle" disabled="true"
								v-model.trim="vehicleData.vehicle_code" />
							<view class="history-vehicle  g-flex g-flex-column" v-if="showHisVehicle">
								<view class="cu-form-group history-vehicle-item" v-for="(item,index) in rechargeList"
									:key="index" @click="saveVehicle(item)">
									<view class="value">{{item}}</view>
								</view>
								<view class="cu-form-group history-vehicle-item" @click="showPlate">
									<view class="value">充值其他车辆</view>
								</view>
							</view>
							<plate-input style="margin-left: 40upx;" v-if="plateShow" :plate="vehicleData.vehicle_code"
								@export="setPlate" @close="plateShow=false" />
						</view>

					</view>

				</view>
				<view class="vux-x-input weui-cell weui-cell_picker" v-if="false">
					<view class="weui-cell__hd">
						<view class="weui-label weui-label__require">车牌颜色</view>
					</view>
					<view class=" weui-cell__primary " style="width: 100%;">
						<picker @change="vehicleColorChange" :range="vehicleColorOptions" range-key="label">
							<view class="weui-picker-value g-flex g-flex-start" v-if="!vehicleColorIndex">
								请选择车牌颜色
							</view>
							<view class="weui-picker-value g-flex g-flex-start" v-else>
								{{vehicleColorOptions[vehicleColorIndex].label}}
							</view>
						</picker>
					</view>
					<view class=" weui-cell__bd weui-cell__ft">
					</view>
				</view>

			</view>
		</view>
		<view class="sellPay-Info">
			<view class="c-title">查询账户信息</view>
			<form v-if="vehicleInfo.vehicle_code">
				<view class="weui-cells">
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label">用户名称</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-cell__value">
								{{noPassByName(vehicleInfo.customer_name)}}
							</view>
						</view>
					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label">车牌号码</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-cell__value">
								{{vehicleInfo.vehicle_code}} 【{{vehicleColorStr}}】
							</view>
						</view>

					</view>
					<view class="vux-x-input weui-cell">
						<view class="weui-cell__hd">
							<view class="weui-label">ETC卡号</view>
						</view>
						<view class="weui-cell__bd weui-cell__primary">
							<view class="weui-cell__value">
								{{noPassByCardNo(vehicleInfo.card_no)}}
							</view>
						</view>

					</view>
					<view class="" v-if="vehicleInfo.gx_card_type=='0'">
						<view class="vux-x-input weui-cell">
							<view class="weui-cell__hd">
								<view class="weui-label">卡账金额</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-cell__value">
									{{moneyFilter(moneyData.card_money)}}元
								</view>
							</view>

						</view>
						<view class="vux-x-input weui-cell">
							<view class="weui-cell__hd">
								<view class="weui-label">卡内未圈存金额</view>
							</view>
							<view class="weui-cell__bd weui-cell__primary">
								<view class="weui-cell__value">
									{{moneyFilter(moneyData.load_money)}}元
								</view>
							</view>

						</view>
					</view>
					<view class="" v-else>
						<view class="weui-cells">
							<view class="vux-x-input weui-cell">
								<view class="weui-cell__hd">
									<view class="weui-label">最低预存金标准值</view>
								</view>
								<view class="weui-cell__bd weui-cell__primary">
									<view class="weui-cell__value">
										{{ moneyFilter(moneyData.black_line)}}元
									</view>
								</view>
							</view>
							<view class="vux-x-input weui-cell">
								<view class="weui-cell__hd">
									<view class="weui-label">账户可用余额</view>
								</view>
								<view class="weui-cell__bd weui-cell__primary">
									<view class="weui-cell__value">
										{{ moneyFilter(trueMoney)}}元
									</view>
								</view>

							</view>

						</view>
					</view>
				</view>

			</form>
		</view>
		<view class="sellPay-Info">
			<view class="c-title">请选择充值金额</view>
			<view class="amount-box g-flex g-flex-wrap ">
				<view v-for="(item,index) in amountList" :key="index" class="amount-item"
					@click="selectAmout(item,index)"
					:style="(amountMoneyIndex!=null&&amountMoneyIndex==index)?'border:1px solid #1978ec':''">
					<view class="g-flex g-flex-center g-flex-align-center">
						<view class="amount-text">
							{{item.label}}
						</view>
						<view class="amount-item-label">元</view>
					</view>
				</view>
				<view class="amount-item g-flex g-flex-align-center g-flex-justify" style="width: 60%;padding: 0 20upx;"
					:style="isAnyMount?'border:1px solid #1978ec':''">
					<input type="digit" @focus="clickInput" class="amount-custom-input" placeholder="其他支付金额" name="b"
						v-model="otherMoney" @input="(e)=> handleInput('otherMoney',e)"></input>
					<text style="margin-right: 10upx;">元</text>
				</view>
			</view>

		</view>
		<view class="sellPay-Info">
			<view class="c-title">充值信息</view>
			<form>
				<view class="cu-form-group">
					<view class="title">支付方式</view>
					<input placeholder="" value="微信支付" name="a" disabled></input>
					<text class="cuIcon-right"></text>
				</view>
				<!-- <view class="cu-form-group">
					<view class="title">支付金额</view>
					<input type="digit" placeholder="支付金额" name="b" :value="formData.money"
						@input="(e)=> handleInput('money',e)"></input>
					<text>元</text>
				</view> -->
			</form>
		</view>
		<view class="certification">
			<TButton title="去充值" @clickButton="goPay" :isLoadding="isBtnLoader" />
		</view>
		<TModal :showModal='dialogVisible' modalTitle='请仔细核对充值信息' :showCancelFlag='true' @okModal='onPayHandle'
			@cancelModal='dialogVisible=false' okText='去支付'>
			<form slot='content' class="sellPay-Info">
				<view class="cu-form-group">
					<view class="title">充值账户:</view>
					<view class="value">{{vehicleInfo.card_no|noPassByCardNo}}</view>
				</view>
				<view class="cu-form-group">
					<view class="title">充值车辆:</view>
					<view class="value">{{vehicleInfo.vehicle_code}}</view>
				</view>
				<view class="cu-form-group">
					<view class="title">充值金额:</view>
					<view class="value">{{formData.money}}元</view>
				</view>
			</form>
		</TModal>

		<TModal :showModal='checkDialogVisible' modalTitle='请确认车主信息' :showCancelFlag='true' @okModal='checked'
			@cancelModal='hideCheckDialogVisible' okText='确认'>
			<form slot='content' class="sellPay-Info">
				<view style="color: #fa5151;font-size: 28rpx;font-weight: bold;margin: 20rpx 0;">
					请填写车主中文姓名首字，以确认车主身份
				</view>
				<view class="check-box g-flex g-flex-center">
					<view class="checkname g-flex g-flex-horizontal-vertical">
						<input class="name-input" v-model="checkFirstName" :focus='isFocus'></input>
						<text class="checkname_des">{{hideSecondName}}</text>
					</view>
				</view>


			</form>
		</TModal>

		<!-- 跳转提示框 -->
		<TModal :showModal='jumpDialogVisible' modalTitle='提示' :showCancelFlag='true' @okModal='jumpLoad'
			@cancelModal='jumpLoadCancel' okText='前往圈存' cancelText='返回'>
			<view slot='content' class="jump-dialog">
				<view class="jump-text">
					{{rechargeStatus+'！3秒后将自动跳转圈存操作界面，请勿退出'}}
				</view>
				<view class="jump-tips">
					(温馨提示:充值到帐可能会有延迟)
				</view>
			</view>
		</TModal>
		<tLoading :isShow="isLoading" />
	</view>
</template>
<script>
	import tLoading from '@/components/common/t-loading.vue';
	import TButton from "@/components/t-button.vue";
	import TModal from '@/components/t-modal/t-modal.vue'
	import {
		payTypeOptions,
		vehicleColorPicker,
		amountList
	} from '@/common/const/optionData.js'

	import {
		getVehicleColor,
		getVehicleClassType,
		getVehicleType,
		gxCardTypeFilter,
	} from '@/common/method/filter.js';

	import {
		getAccountId,
		getOpenid,
		getLoginUserInfo,
		setOpenid
	} from "@/common/storageUtil.js";
	import plateInput from '@/components/uni-plate-input/uni-plate-input'
	import float from '@/common/method/float.js'
	import {
		twoDecimal,
		moneyFilter,
		noPassByCardNo,
		noPassByName
	} from '@/common/util.js'
	export default {
		components: {
			TButton,
			TModal,
			tLoading,
			plateInput
		},
		data() {
			return {
				vehicleColorOptions: vehicleColorPicker,
				isLoading: false,
				isBtnLoader: false,
				isRecycleVisible: false,
				dialogVisible: false,
				vehicleInfo: {
					vehicle_code: "",
					vehicle_color: "",
					card_no: "",
					customer_name: ""
				},
				cardAmount: {},
				formData: {
					"recharge_type": "********", //充值方式
					"card_no": "", //卡号
					"money": '', //充值金额
					"bank_num": "",
					"bank_name": "",
					"source": "3", //来源
					"auth_code": "",
					"open_id": "",
					"tradeType": 'CARD_RECHARGE', //场景
					'agent_user': '', //代充人
				},
				vehicleReg: /^([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z0-9]{1}[A-Z0-9]{1}([京津沪渝桂蒙宁新藏冀晋辽吉黑苏浙皖赣闽鲁粤鄂湘豫川云贵陕甘青琼])?[A-NP-Z0-9]{1}[A-NP-Z0-9]{3}[A-NP-Z0-9挂学警港澳领试超外应急]{1}([A-NP-Z0-9外应急])?|^([A-Z0-9]{7}))$/,
				vehicleData: {
					vehicle_color: "",
					vehicle_code: "",
				},
				vehicleColorIndex: 0,
				plateShow: false,
				amountList,
				amountMoneyIndex: null,
				otherMoney: '',
				isAnyMount: false,
				checkDialogVisible: false,
				checkFirstName: "",
				hideFirstName: '',
				isFocus: false,
				moneyData: {},
				rechargeList: [],
				showHisVehicle: false,
				hideSecondName: '',
				rechargeStatus: '',
				jumpDialogVisible: false,
				countdowner: null, //倒计时定时器
				trueMoney: ''
			};
		},
		onUnload() {
			clearTimeout(this.countdowner)
		},
		onLoad(options) {},
		computed: {
			vehicleColorStr() {
				return getVehicleColor(this.vehicleInfo.vehicle_color);
			},
		},
		created() {
			this.formData.agent_user = getLoginUserInfo().userNo;
			this.formData.open_id = getOpenid() || ''
			//#ifdef  MP-WEIXIN
			this.getOpenIdHandle();
			// #endif
			this.getRechargeList()
		},
		watch: {
			"vehicleData.vehicle_color": function(val) {
				if (this.vehicleReg.test(this.vehicleData.vehicle_code)) {
					this.getVehicleInfo();
				}
			},
			'vehicleData.vehicle_code': function(val) {
				if (this.vehicleData.vehicle_color && val && this.vehicleReg.test(val)) {
					this.vehicleData.vehicle_code = val.toUpperCase()
					this.getVehicleInfo();
				}
			}
		},
		methods: {
			gxCardTypeFilter,
			moneyFilter,
			noPassByCardNo,
			noPassByName,
			onLicensecolorChange(val) {
				this.vehicleData.vehicle_color = val;
			},
			getOpenIdHandle() {

				if (getOpenid()) return;
				let _self = this;
				wx.login({
					success(res) {
						let params = {
							code: res.code
						}

						_self.$request.post(_self.$interfaces.getOpenid, {
							data: params
						}).then((res) => {
							if (res.code == 200) {
								if (res.data && res.data.openid) {
									_self.formData.open_id = res.data.openid
									setOpenid(res.data.openid)

								}

							}
						})
					}
				})
			},
			//弹框确认圈存取消
			jumpLoadCancel() {
				uni.reLaunch({
					url: "/pagesB/rechargeBusiness/selectVehicle/index"
				})
				this.jumpDialogVisible = false
			},
			//弹框确认圈存确定
			jumpLoad() {
				uni.reLaunch({
					url: '/pagesB/loadBusiness/loadType'
				})
				this.jumpDialogVisible = false
			},
			getRechargeList() {
				let data = {
					routePath: this.$interfaces.rechargeOther.method,
					bizContent: {
						userNo: getLoginUserInfo().userNo,
						pageNum: 0,
						pageSize: 20
					}
				}
				this.isLoading = true
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then(res => {
						console.log(res, '代充记录');
						if (res.code == 200) {
							this.isLoading = false
							this.rechargeList = []
							if (res.data.records.length != 0) {
								let arr = []
								arr = res.data.records.filter(item => {
									if (item.payStatus == '2' || item.payStatus == '4') {
										return item
									}
								})
								arr = arr.map(item => {
									return item.carNo
								})
								this.rechargeList = [...new Set(arr)].slice(0, 3)
							}

						} else {
							this.isLoading = false
							uni.showModal({
								title: '提示',
								content: res.msg,
								showCancel: false
							});
						}
					})
					.catch(err => {
						this.isLoading = false
						uni.showModal({
							title: '提示',
							content: err.msg,
							showCancel: false
						});
					});
			},
			//选择历史车辆或输入车牌号
			selectVehicle() {
				if (this.rechargeList.length == 0) {
					this.plateShow = true
					return
				}
				this.showHisVehicle = true
			},
			saveVehicle(item) {
				this.vehicleData.vehicle_code = item
				this.showHisVehicle = false
			},
			showPlate() {
				this.vehicleData.vehicle_code = ''
				this.plateShow = true
				this.showHisVehicle = false
			},
			//选择金额
			selectAmout(item, index) {
				this.otherMoney = ''
				this.formData.money = ''
				this.amountMoneyIndex = index
				this.formData.money = item.value
				this.isAnyMount = false
			},

			//input聚焦事件
			clickInput() {
				this.amountMoneyIndex = null
				this.formData.money = ''
				this.isAnyMount = true
			},
			//输入车牌控件
			setPlate(plate) {
				if (plate.length >= 7) this.vehicleData.vehicle_code = plate
				this.plateShow = false
			},

			vehicleColorChange(e) {
				this.vehicleColorIndex = e.detail.value;
				this.vehicleData.vehicle_color = this.vehicleColorOptions[e.detail.value].value || "";
			},
			getAmountInfo() {
				let data = {
					routePath: this.$interfaces.loadCardAmount.method,
					bizContent: {
						cpu_card_id: this.vehicleInfo.card_no
					}
				}
				this.$request
					.post(this.$interfaces.issueRoute, {
						data: data
					})
					.then((res) => {
						console.log(res, '查询账户余额')
						if (res.code == 200) {
							this.moneyData = res.data
							this.trueMoney = float.sub(this.moneyData.card_money, this.moneyData.black_line)
							//判断是否存在圈存异常锁定
							let lockFlag = float.add(res.data.lock_money, res.data.card_return_loading) > 0
							if ((res.data.load_money > 0 || res.data.card_money > 0 || lockFlag) && this.vehicleInfo
								.gx_card_type == '0') {
								uni.showModal({
									title: "提示",
									content: lockFlag ? `查出该储值卡有异常被锁金额，请先前往圈存页面进行圈存异常处理` :
										"查询出该储值卡有可圈存余额，请先去圈存",
									showCancel: false,
									success: () => {
										if (res) {
											uni.redirectTo({
												url: '/pagesB/loadBusiness/loadType'
											})
										}
									}
								});
								return
							}
							if (float.add(res.data.card_money, res.data.load_money) < 0 && this.vehicleInfo
								.gx_card_type == '0') {
								uni.showModal({
									title: "提示",
									content: "查询出该储值卡有欠费，请先补缴欠费！",
									showCancel: false,
								});
								return
							}
						}
					})
			},
			//查询车辆信息
			getVehicleInfo() {
				let params = {
					vehicle_code: this.vehicleData.vehicle_code,
					vehicle_color: this.vehicleData.vehicle_color
				}
				this.isLoading = true
				this.$request.post(this.$interfaces.queryVehicleInfo, {
					data: params
				}).then(res => {
					if (res.code == 200) {
						this.isLoading = false
						console.log(res, '查询账户信息');
						if (res.data && res.data.length) {
							if (res.data[0].gx_card_type != 5 && res.data[0].gx_card_type != 0 && res.data[0]
								.gx_card_type != 8) {
								uni.showModal({
									title: "提示",
									content: this.vehicleData.vehicle_code + '绑定的卡不是捷通日日通记账卡或储值卡，无法充值',
									showCancel: false,
									success: function(res) {
										if (res.confirm) {
											uni.navigateBack({
												delta: 1
											})
										}
									}
								});
								return
							}
							// if (res.data[0].gx_card_type == 0) {
							// 	uni.navigateTo({
							// 		url: '../../loadBusiness/recharge/recharge?vehicleInfoData=' + JSON
							// 			.stringify(res.data[0])
							// 	})
							// 	this.vehicleData.vehicle_code = ''
							// 	this.vehicleData.vehicle_color = '0'
							// 	return
							// }
							this.vehicleInfo = res.data[0]
							this.vehicleInfo.vehicle_code = res.data[0].vehicle_code
							this.vehicleInfo.vehicle_color = res.data[0].vehicle_color
							this.vehicleInfo.card_no = res.data[0].cpu_card_id
							this.vehicleInfo.customer_name = res.data[0].customer_name
							this.hideFirstName = (res.data[0].customer_name).substring(1)
							if (this.hideFirstName.length > 1) {
								this.hideSecondName = "*" + this.hideFirstName.substring(1, this.hideFirstName
									.length)
							} else {
								this.hideSecondName = this.hideFirstName
							}
							this.getAmountInfo()

						}

					} else {
						this.vehicleInfo.vehicle_code = ''
						this.vehicleInfo.vehicle_color = ''
						this.vehicleInfo.card_no = ''
						this.vehicleInfo.customer_name = ''
						this.isLoading = false
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false
						});
					}
				})
			},
			// 输入框
			handleInput(type, event) {
				if (type == 'otherMoney') {
					this.formData.money = event.target.value
				} else {
					this.formData[type] = event.target.value
				}
			},

			onPayHandle() {
				this.loadRecharge();
			},
			validate() {
				if (!this.vehicleData.vehicle_color) {
					uni.showModal({
						title: "提示",
						content: "请选择代充车牌颜色",
						showCancel: false,

					});
					return false;
				}
				if (!this.vehicleData.vehicle_code) {
					uni.showModal({
						title: "提示",
						content: "请输入代充车牌号码",
						showCancel: false,

					});
					return false;
				}
				if (!this.vehicleInfo.vehicle_code) {
					uni.showModal({
						title: "提示",
						content: "查询代充车牌号码不存在",
						showCancel: false,

					});
					return false;
				}
				if (!this.formData.money) {
					uni.showModal({
						title: "提示",
						content: "请输入充值金额",
						showCancel: false,

					});
					return false;
				}
				if (!twoDecimal(this.formData.money)) {
					uni.showModal({
						title: "提示",
						content: "充值金额必须大于零并最多保留小数点后两位！",
						showCancel: false,
					});
					return false;
				}
				return true;
			},
			//
			goPay() {
				if (!this.validate()) return
				this.checkDialogVisible = true
				this.isFocus = true
				//2022.4.06优化，代人充值前校验姓名

			},
			//
			checked() {
				let fullname = this.checkFirstName + this.hideFirstName
				if (fullname !== this.vehicleInfo.customer_name) {
					uni.showModal({
						title: "提示",
						content: "您输入的中文名首字和查询出的不符，请重试！",
						showCancel: false,
					});
					return

				}
				this.checkDialogVisible = false
				this.isFocus = false
				this.checkFirstName = ''
				this.dialogVisible = true;
			},
			hideCheckDialogVisible() {
				this.checkDialogVisible = false
				this.isFocus = false
				this.checkFirstName = ''
			},
			//充值
			loadRecharge() {
				let _self = this;
				this.isLoading = true;
				this.formData.card_no = this.vehicleInfo.card_no
				let params = JSON.parse(JSON.stringify(this.formData))
				if (this.vehicleInfo.gx_card_type == '0') {
					params.tradeType = 'CARD_LOAD'
				}
				this.dialogVisible = false;
				params.money = float.mul(params.money, 100)
				let data = {
					routePath: this.$interfaces.loadRecharge.method,
					bizContent: params
				}
				this.$request.post(this.$interfaces.issueRoute, {
					data: data
				}).then(res => {
					this.isLoading = false;
					if (res.code == 200) {
						this.formData.money = ''
						let data = res.data;
						let payMessage = res.data.payMessage ? JSON.parse(res.data.payMessage) : {};
						//拉起微信支付
						wx.requestPayment({
							...payMessage,
							"success": function(res) {
								console.log(res);
								_self.isLoading = true;
								//异步调用，确保能查到状态
								setTimeout(() => {
									_self.loadRechargeQuery(data);
								}, 6000)
							},
							"fail": function(res) {

							},
							"complete": function(res) {}
						})
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg,
							showCancel: false,
						});
					}
				}).catch(error => {
					this.isLoading = false;
					uni.showModal({
						title: "提示",
						content: error.msg,
						showCancel: false,
					});
				})
			},

			//查询充值结果
			loadRechargeQuery(data) {
				let params = {
					routePath: this.$interfaces.loadRechargeQuery.method,
					bizContent: {
						order_Id: data.order_id
					}
				}
				this.isLoading = false;
				this.$request.post(this.$interfaces.issueRoute, {
					data: params
				}).then(res => {
					this.otherMoney = ''
					this.amountMoneyIndex = null
					let status = res.data.status;
					let statusVal = {
						1: "充值中",
						2: "充值成功",
						3: "充值失败",
					};
					let msg = statusVal[status] || "充值失败";
					if (this.vehicleInfo.gx_card_type == '0') {
						this.rechargeStatus = msg
						this.jumpDialogVisible = true
						this.countdowner = setTimeout(() => {
							uni.reLaunch({
								url: '/pagesB/loadBusiness/loadType'
							})
							this.jumpDialogVisible = false
							clearTimeout(this.countdowner)
						}, 3000)
						// uni.showModal({
						// 	title: "提示",
						// 	content: msg + '！3秒后将自动跳转圈存操作界面，请勿退出(温馨提示:充值到帐可能会有延迟)',
						// 	confirmText: '确定',
						// 	success: function(res) {
						// 		if (res.confirm) {
						// 			clearTimeout(time)
						// 			uni.reLaunch({
						// 				url: '/pagesB/loadBusiness/loadType'
						// 			})
						// 		} else {
						// 			uni.reLaunch({
						// 				url: "/pagesB/rechargeBusiness/selectVehicle/index"
						// 			})
						// 		}
						// 	}
						// });
						return
					}

					uni.showModal({
						title: "提示",
						content: msg + '！是否查看充值记录(温馨提示:充值到帐可能会有延迟)',
						confirmText: '查看',
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pagesB/rechargeBusiness/rechargeList/p-rechargeList?cardNo=' +
										this.vehicleInfo.card_no
								})
							} else {
								uni.reLaunch({
									url: "/pagesB/rechargeBusiness/selectVehicle/index"
								})
							}
						}

					});
				})
			},
		},
		filters: {
			//脱敏用户名
			noPassByName(str) {
				if (null != str && str != undefined) {
					if (str.length <= 2 && str.length > 0) {
						return "*" + str.substring(1, str.length);
					} else {
						return "**" + str.substring(2, str.length);
					}
				} else {
					return "";
				}
			},
			//脱敏etc卡号
			noPassByCardNo(str) {
				if (null != str && str != undefined) {
					var pat = /(\d{3})\d*(\d{4})/;
					return str.replace(pat, '$1*************$2');
				} else {
					return "";
				}

			}
		},
	};
</script>
<style lang="scss" scoped>
	.sellPay-Info {
		background-color: #FFFFFF;

		.c-title {
			margin-top: 30upx;
			padding: 0 25upx;
			font-size: 28upx;
			line-height: 80upx;
			font-weight: bold;
			color: #000;
			background: #ffffff;
		}

		.amount-box {

			padding: 20rpx;

			.amount-item {
				margin: 10upx 20upx;
				width: 27%;
				height: 110upx;
				background-color: #f8f9fe;
				border-radius: 10px;
				position: relative;

				.amount-text {
					line-height: 100upx;
					color: #1978ec;
					font-size: 34upx;
					font-weight: bold;
				}

				.amount-item-label {
					color: #1978ec;
					font-size: 26upx;
				}


			}
		}
	}

	.certification {
		padding: 0 25upx;
		margin-top: 30upx;
	}

	.sellPay-Info .cu-form-group .value {
		-webkit-box-flex: 1;
		-webkit-flex: 1;
		flex: 1;
		font-size: 30rpx;
		color: #555;
		padding-right: 20rpx;
		text-align: left;
	}

	.sellPay-Info .cu-form-group input {
		text-align: left;
	}

	.sellPay-Info .cu-form-group radio-group {

		flex: 1;
		text-align: left;
	}

	.check-box {
		width: 90%;
		margin: 0 auto;
		padding-bottom: 20rpx;
	}

	.checkname {
		width: 100%;
	}

	.name-input {
		border: 1px solid #000000;
		width: 70rpx;
		height: 70rpx;
		display: block;
		margin-right: 10rpx;
	}

	.checkname_des {
		max-width: 400rpx;
		word-break: break-all;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		/* 这里是超出几行省略 */
		overflow: hidden;
		text-align: left;
	}

	.input-wrapper {
		position: relative;
	}

	.history-vehicle {
		position: absolute;
		border-radius: 20rpx;
		z-index: 9999;

		.history-vehicle-item {
			min-height: 70rpx;
			background: #f3f3f3;

		}
	}

	.weui-label {
		width: 280rpx;
	}

	.weui-cells {
		padding-top: 0rpx;
	}

	.jump-dialog {
		font-size: 32rpx;
		padding: 30rpx;

		.jump-tips {
			font-size: 26rpx;
			color: #969696;
			margin-top: 30rpx;
		}
	}
</style>