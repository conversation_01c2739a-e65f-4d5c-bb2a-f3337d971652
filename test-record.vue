<template>
  <view class="test-container">
    <view class="header">
      <text class="title">录音功能测试</text>
    </view>
    
    <view class="status-section">
      <text class="status-text">录音状态: {{ isRecording ? '录音中' : '未录音' }}</text>
      <text class="status-text">识别状态: {{ isRecognizing ? '识别中' : '未识别' }}</text>
      <text class="status-text">录音时长: {{ formatDuration(recordDuration) }}</text>
    </view>
    
    <view class="control-section">
      <button 
        class="record-btn" 
        :class="{ 'recording': isRecording }"
        @touchstart="startRecord"
        @touchend="stopRecord"
      >
        {{ isRecording ? '松开结束录音' : '按住开始录音' }}
      </button>
    </view>
    
    <view class="result-section">
      <text class="result-title">识别结果:</text>
      <text class="result-text">{{ recognitionResult || '暂无结果' }}</text>
    </view>
    
    <view class="log-section">
      <text class="log-title">日志:</text>
      <scroll-view class="log-content" scroll-y>
        <text v-for="(log, index) in logs" :key="index" class="log-item">
          {{ log }}
        </text>
      </scroll-view>
    </view>
  </view>
</template>

<script>
// 引入语音相关插件
const plugin = requirePlugin("WechatSI")
const recorderManager = uni.getRecorderManager()

export default {
  name: 'TestRecord',
  data() {
    return {
      isRecording: false,
      isRecognizing: false,
      recordDuration: 0,
      recordStartTime: 0,
      recordTimer: null,
      recognitionResult: '',
      logs: []
    }
  },
  created() {
    this.initRecorderManager()
  },
  methods: {
    // 添加日志
    addLog(message) {
      const timestamp = new Date().toLocaleTimeString()
      this.logs.unshift(`[${timestamp}] ${message}`)
      if (this.logs.length > 20) {
        this.logs.pop()
      }
    },
    
    // 初始化录音管理器
    initRecorderManager() {
      this.addLog('初始化录音管理器')
      
      recorderManager.onStart(() => {
        this.addLog('录音开始')
        this.recordStartTime = Date.now()
        this.recordDuration = 0
        
        this.recordTimer = setInterval(() => {
          this.recordDuration = Date.now() - this.recordStartTime
        }, 100)
      })
      
      recorderManager.onStop((res) => {
        this.addLog(`录音停止，文件路径: ${res.tempFilePath}`)
        
        if (this.recordTimer) {
          clearInterval(this.recordTimer)
          this.recordTimer = null
        }
        
        if (this.recordDuration < 1000) {
          this.addLog('录音时间太短')
          uni.showToast({
            title: '录音时间太短',
            icon: 'none'
          })
          return
        }
        
        this.startVoiceRecognition(res.tempFilePath)
      })
      
      recorderManager.onError((res) => {
        this.addLog(`录音错误: ${JSON.stringify(res)}`)
        this.isRecording = false
        this.isRecognizing = false
        
        if (this.recordTimer) {
          clearInterval(this.recordTimer)
          this.recordTimer = null
        }
      })
    },
    
    // 开始录音
    startRecord() {
      this.addLog('开始录音')
      this.isRecording = true
      this.recognitionResult = ''
      
      recorderManager.start({
        duration: 60000,
        sampleRate: 16000,
        numberOfChannels: 1,
        encodeBitRate: 96000,
        format: 'mp3',
        frameSize: 50
      })
    },
    
    // 停止录音
    stopRecord() {
      this.addLog('停止录音')
      this.isRecording = false
      recorderManager.stop()
    },
    
    // 语音识别
    startVoiceRecognition(filePath) {
      this.addLog('开始语音识别')
      this.isRecognizing = true
      
      plugin.speechToText({
        filePath: filePath,
        lang: 'zh_CN',
        success: (res) => {
          this.addLog(`识别成功: ${res.result}`)
          this.isRecognizing = false
          this.recognitionResult = res.result || '识别结果为空'
        },
        fail: (err) => {
          this.addLog(`识别失败: ${JSON.stringify(err)}`)
          this.isRecognizing = false
          this.recognitionResult = '识别失败'
        }
      })
    },
    
    // 格式化时长
    formatDuration(duration) {
      const seconds = Math.floor(duration / 1000)
      const ms = duration % 1000
      return `${seconds}.${Math.floor(ms / 100)}s`
    }
  },
  
  beforeDestroy() {
    if (this.recordTimer) {
      clearInterval(this.recordTimer)
    }
    if (this.isRecording) {
      recorderManager.stop()
    }
  }
}
</script>

<style lang="scss" scoped>
.test-container {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.status-section {
  background: white;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  
  .status-text {
    display: block;
    font-size: 28rpx;
    color: #666;
    margin-bottom: 16rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.control-section {
  text-align: center;
  margin-bottom: 30rpx;
  
  .record-btn {
    width: 300rpx;
    height: 100rpx;
    background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
    color: white;
    border: none;
    border-radius: 50rpx;
    font-size: 28rpx;
    
    &.recording {
      background: linear-gradient(135deg, #FF6B6B 0%, #EE5A52 100%);
      animation: pulse 1s infinite;
    }
  }
}

.result-section {
  background: white;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  
  .result-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    display: block;
    margin-bottom: 16rpx;
  }
  
  .result-text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
  }
}

.log-section {
  background: white;
  padding: 30rpx;
  border-radius: 16rpx;
  height: 400rpx;
  
  .log-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    display: block;
    margin-bottom: 16rpx;
  }
  
  .log-content {
    height: 300rpx;
    
    .log-item {
      display: block;
      font-size: 24rpx;
      color: #666;
      margin-bottom: 8rpx;
      padding: 8rpx;
      background: #f8f8f8;
      border-radius: 8rpx;
    }
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}
</style>
