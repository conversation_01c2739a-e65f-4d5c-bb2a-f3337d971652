# 录音权限错误修复指南

## 错误信息
```
stop:fail api scope is not declared in the privacy agreement
```

## 快速修复步骤

### 1. 代码配置修改 ✅ (已完成)

在 `manifest.json` 中已添加：

```json
{
  "mp-weixin": {
    "permission": {
      "scope.record": {
        "desc": "需要使用你的麦克风权限，用于录音和语音识别功能"
      }
    },
    "requiredPrivateInfos": ["record"]
  }
}
```

### 2. 微信小程序后台配置 (需要手动操作)

#### 登录微信小程序后台
1. 访问：https://mp.weixin.qq.com/
2. 登录小程序管理员账号
3. 选择对应的小程序

#### 配置隐私保护指引
1. 进入 **设置** → **基本设置**
2. 找到 **隐私设置** 部分
3. 点击 **隐私保护指引设置**
4. 添加录音功能说明：

```
录音功能：
- 用途：语音输入和语音识别
- 场景：用户选择语音输入时
- 数据处理：录音数据仅用于语音识别，不会存储
- 第三方：使用微信同声传译插件
```

### 3. 重新提交审核
1. 保存隐私协议设置
2. 重新上传小程序代码
3. 提交审核
4. 等待审核通过后发布

### 4. 代码优化 ✅ (已完成)

已添加隐私协议检查：

```javascript
// 检查隐私协议和录音权限
checkRecordPermission() {
  return new Promise((resolve, reject) => {
    if (uni.requirePrivacyAuthorize) {
      uni.requirePrivacyAuthorize({
        success: () => {
          this.checkRecordAuth(resolve, reject)
        },
        fail: () => {
          uni.showToast({
            title: '需要同意隐私协议才能使用录音功能',
            icon: 'none'
          })
          reject(false)
        }
      })
    } else {
      this.checkRecordAuth(resolve, reject)
    }
  })
}
```

## 临时解决方案

如果暂时无法完成后台配置，可以使用模拟录音功能：

### 1. 修改录音开始方法
```javascript
handleVoiceStart() {
  // 临时跳过权限检查，直接使用模拟
  this.isRecording = true
  uni.vibrateShort()
  
  // 模拟录音过程
  this.simulateRecording()
}

simulateRecording() {
  this.recordStartTime = Date.now()
  this.recordDuration = 0
  
  this.recordTimer = setInterval(() => {
    this.recordDuration = Date.now() - this.recordStartTime
  }, 100)
}
```

### 2. 修改录音结束方法
```javascript
handleVoiceEnd() {
  this.isRecording = false
  
  if (this.recordTimer) {
    clearInterval(this.recordTimer)
    this.recordTimer = null
  }
  
  // 直接使用模拟识别
  this.simulateVoiceRecognition()
}
```

## 验证步骤

### 1. 开发环境测试
- 在微信开发者工具中测试
- 检查控制台是否还有权限错误
- 验证录音功能是否正常

### 2. 真机测试
- 使用真机预览测试
- 检查权限申请流程
- 验证语音识别功能

### 3. 发布后验证
- 发布正式版本
- 用户反馈收集
- 功能使用数据监控

## 常见问题解决

### Q1: 配置后仍然报错
**解决方案：**
- 检查 `manifest.json` 配置是否正确
- 确认后台隐私协议是否保存
- 重新编译和上传代码

### Q2: 用户拒绝隐私协议
**解决方案：**
- 提供更清晰的功能说明
- 添加"跳过录音功能"选项
- 引导用户了解录音功能价值

### Q3: 权限申请失败
**解决方案：**
- 检查设备录音权限设置
- 引导用户到系统设置开启
- 提供文字输入替代方案

## 最佳实践

### 1. 权限申请时机
- 在用户主动选择录音功能时申请
- 不要在应用启动时就申请所有权限
- 提供清晰的权限使用说明

### 2. 用户体验优化
- 权限被拒绝时提供替代方案
- 清晰的错误提示和解决指引
- 允许用户随时关闭录音功能

### 3. 隐私保护
- 录音数据不上传到自己服务器
- 使用官方插件确保数据安全
- 及时清理临时录音文件

## 检查清单

- [ ] `manifest.json` 配置正确
- [ ] 微信后台隐私协议已配置
- [ ] 代码中添加隐私协议检查
- [ ] 权限申请流程完善
- [ ] 错误处理机制完整
- [ ] 提供替代输入方案
- [ ] 真机测试通过
- [ ] 提交审核并发布

完成以上步骤后，录音功能应该可以正常使用！
