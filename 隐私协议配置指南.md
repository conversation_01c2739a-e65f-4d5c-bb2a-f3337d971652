# 微信小程序隐私协议配置指南

## 问题描述
错误信息：`stop:fail api scope is not declared in the privacy agreement`

这个错误表示录音功能没有在微信小程序的隐私协议中声明。

## 解决方案

### 1. 代码配置修改

已在 `manifest.json` 中添加了必要的配置：

```json
{
  "mp-weixin": {
    "permission": {
      "scope.userLocation": {
        "desc": "你的位置信息将用于小程序位置接口的效果展示"
      },
      "scope.record": {
        "desc": "需要使用你的麦克风权限，用于录音和语音识别功能"
      }
    },
    "requiredPrivateInfos": [
      "chooseAddress", 
      "getLocation", 
      "chooseLocation", 
      "record"
    ]
  }
}
```

### 2. 微信小程序后台配置

#### 步骤1：登录微信小程序后台
1. 访问 [微信公众平台](https://mp.weixin.qq.com/)
2. 使用小程序管理员账号登录
3. 进入对应的小程序管理后台

#### 步骤2：配置隐私协议
1. 在左侧菜单中找到 **"设置"** → **"基本设置"**
2. 向下滚动找到 **"隐私设置"** 部分
3. 点击 **"隐私保护指引设置"**

#### 步骤3：添加录音权限说明
1. 在隐私保护指引中添加以下内容：

```
录音功能说明：
- 用途：用于语音输入和语音识别功能
- 场景：当用户选择语音输入方式时
- 权限：需要访问设备麦克风进行录音
- 数据处理：录音数据仅用于语音识别转换为文字，不会存储或上传个人语音数据
- 第三方服务：使用微信同声传译插件进行语音识别
```

#### 步骤4：配置用户隐私保护指引
在隐私保护指引中需要包含：

1. **收集的信息类型**
   - 录音数据（临时）
   - 位置信息
   - 设备信息

2. **信息使用目的**
   - 录音：语音转文字功能
   - 位置：提供基于位置的服务推荐
   - 设备：优化用户体验

3. **信息存储和处理**
   - 录音数据：仅在本地临时存储，识别完成后自动删除
   - 不会永久保存用户语音数据
   - 使用微信官方插件进行语音识别

4. **用户权利**
   - 用户可以随时关闭录音权限
   - 用户可以选择不使用语音功能
   - 提供其他输入方式（文字输入）

### 3. 隐私协议模板

```
隐私保护指引

一、我们收集的信息
1. 录音信息：当您使用语音输入功能时，我们会临时收集您的录音数据
2. 位置信息：用于为您提供基于位置的旅游推荐服务
3. 设备信息：用于优化小程序性能和用户体验

二、信息的使用
1. 录音数据仅用于语音识别转换为文字，识别完成后立即删除
2. 位置信息用于推荐附近的景点、酒店、美食等
3. 设备信息用于适配不同设备的显示效果

三、信息的存储和保护
1. 录音数据不会上传到我们的服务器，仅在您的设备上临时处理
2. 使用微信官方的同声传译插件进行语音识别
3. 我们采用行业标准的安全措施保护您的信息

四、您的权利
1. 您可以随时在设置中关闭录音权限
2. 您可以选择使用文字输入代替语音输入
3. 您可以随时联系我们了解或删除您的信息

五、联系我们
如有任何隐私相关问题，请联系我们：[联系方式]
```

### 4. 发布流程

#### 步骤1：提交审核
1. 完成隐私协议配置后
2. 重新提交小程序代码审核
3. 等待微信审核通过

#### 步骤2：版本发布
1. 审核通过后发布新版本
2. 用户更新到新版本后录音功能正常使用

### 5. 注意事项

1. **隐私协议必须真实**
   - 描述的功能必须与实际使用一致
   - 不能虚假描述数据使用目的

2. **权限申请时机**
   - 在用户首次使用录音功能时申请权限
   - 提供清晰的权限使用说明

3. **数据安全**
   - 录音数据确实不应该上传到自己的服务器
   - 使用微信官方插件确保数据安全

4. **用户体验**
   - 提供权限被拒绝时的替代方案
   - 允许用户随时关闭录音功能

### 6. 常见问题

**Q: 隐私协议配置后多久生效？**
A: 需要重新提交审核，审核通过并发布后生效。

**Q: 用户已经拒绝了录音权限怎么办？**
A: 引导用户到小程序设置页面重新开启权限。

**Q: 可以不配置隐私协议直接使用录音吗？**
A: 不可以，微信要求所有涉及用户隐私的功能都必须在隐私协议中声明。

**Q: 录音数据可以上传到自己的服务器吗？**
A: 可以，但必须在隐私协议中明确说明，并获得用户明确同意。

### 7. 测试验证

配置完成后，可以通过以下方式验证：

1. 在微信开发者工具中测试录音功能
2. 真机调试验证权限申请流程
3. 检查是否还有隐私相关错误

完成以上配置后，录音功能应该可以正常使用了！
